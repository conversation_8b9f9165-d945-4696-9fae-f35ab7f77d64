package com.cowell.iscm.mapperTidb;

import com.cowell.iscm.entityTidb.JymlStoreSkuSuggest;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

@Mapper
@Repository
public interface JymlStoreSkuSuggestMapper {
    List<JymlStoreSkuSuggest> selectByBusinessOrgIdAndStoreIdGoodsNos(@Param("businessOrgId") Long businessOrgId,@Param("storeCode") String storeCode,@Param("goodsNoList") List<String> goodsNoList);
}