package com.cowell.iscm.service;

import com.cowell.iscm.entity.IscmGoodsRegisterOrder;
import com.cowell.iscm.entity.IscmGoodsRegisterOrderDetail;
import com.cowell.iscm.entityTidb.IscmSuggestGoodsAllot;
import com.cowell.iscm.service.feign.dto.SpuListVo;
import com.cowell.iscm.service.feign.dto.StockGoodsCountInfo;
import com.cowell.permission.dto.mdm.MdmStoreBaseDTO;
import com.cowell.permission.vo.OrgVO;

import java.util.List;
import java.util.Map;

/**
 * 调拨辅助服务接口
 *
 * <AUTHOR>
 */
public interface AllotAuxService {
    /**
     * 获取门店信息Map
     *
     * @param iscmSuggestGoodsAllots
     * @return
     * @throws Exception
     */
    Map<String, MdmStoreBaseDTO> getStoreMap(List<IscmSuggestGoodsAllot> iscmSuggestGoodsAllots) throws Exception;

    /**
     * 获取组织信息Map
     *
     * @param iscmSuggestGoodsAllots
     * @return
     * @throws Exception
     */
    Map<String, OrgVO> getOrgMap(List<IscmSuggestGoodsAllot> iscmSuggestGoodsAllots) throws Exception;

    /**
     * 获取商品信息Map
     *
     * @param iscmSuggestGoodsAllots
     * @return
     * @throws Exception
     */
    Map<String, SpuListVo> getGoodsMap(List<IscmSuggestGoodsAllot> iscmSuggestGoodsAllots) throws Exception;

    /**
     * 获取门店组织机构信息
     *
     * @param iscmSuggestGoodsAllots
     * @return
     * @throws Exception
     */
    public Map<String, OrgVO> getStoreOrgMap(List<IscmSuggestGoodsAllot> iscmSuggestGoodsAllots) throws Exception;

    /**
     * 获取库存信息Map
     *
     * @param storeMap
     * @param iscmSuggestGoodsAllots
     * @return
     * @throws Exception
     */
    Map<String, List<StockGoodsCountInfo>> getStockMap(Map<String, MdmStoreBaseDTO> storeMap, List<IscmSuggestGoodsAllot> iscmSuggestGoodsAllots, Boolean groupByBatchNo) throws Exception;

    /**
     * 获取登记信息Map
     *
     * @param iscmSuggestGoodsAllots
     * @return
     * @throws Exception
     */
    Map<String, IscmGoodsRegisterOrder> getRegisterMap(List<IscmSuggestGoodsAllot> iscmSuggestGoodsAllots) throws Exception;

    /**
     * 获取登记信息Map 并且查询 子表登记数量
     *
     * @param iscmSuggestGoodsAllots
     * @return
     * @throws Exception
     */
    Map<String, IscmGoodsRegisterOrderDetail> getRegisterDetailMap(List<IscmSuggestGoodsAllot> iscmSuggestGoodsAllots) throws Exception;

    /**
     * 获取门店名称列表
     *
     * @param storeMap
     * @param iscmSuggestGoodsAllots
     * @return
     * @throws Exception
     */
    List<String> getStoreNames(Map<String, MdmStoreBaseDTO> storeMap, List<IscmSuggestGoodsAllot> iscmSuggestGoodsAllots) throws Exception;

    /**
     * 获取门店调出名称列表
     *
     * @param storeMap
     * @param iscmSuggestGoodsAllots
     * @return
     * @throws Exception
     */
    List<String> getOutStoreNames(Map<String, MdmStoreBaseDTO> storeMap, List<IscmSuggestGoodsAllot> iscmSuggestGoodsAllots) throws Exception;
}
