package com.cowell.iscm.service;

import com.cowell.iscm.service.dto.TokenUserDTO;
import com.cowell.iscm.service.dto.risk.RiskConfigDTO;

import java.util.List;

/**
 * 预警配置服务接口
 *
 * <AUTHOR>
 */
public interface RiskConfigService {

    /**
     * 保存预警配置
     *
     * @param tokenUserDTO
     * @param riskConfigDTO
     * @return
     * @throws Exception
     */
    int save(TokenUserDTO tokenUserDTO, RiskConfigDTO riskConfigDTO) throws Exception;

    /**
     * 获取预警配置
     *
     * @param id
     * @return
     */
    RiskConfigDTO getById(Long id);

    /**
     * 根据riskType获取预警配置
     *
     * @param riskType
     * @param orgId
     * @return
     * @throws Exception
     */
    RiskConfigDTO getByType(Byte riskType, Long orgId) throws Exception;

    /**
     * 获取预警邮箱列表
     *
     * @param riskConfigId
     * @return
     */
    List<String> getEmailList(Long riskConfigId);

    /**
     * 发送邮件
     */
    void sendMail();

    /**
     * 数据更新
     */
    void dataUpdate();

    /**
     * 数据清除
     *
     * @param ids
     */
    void dataClear(List<Long> ids);
}
