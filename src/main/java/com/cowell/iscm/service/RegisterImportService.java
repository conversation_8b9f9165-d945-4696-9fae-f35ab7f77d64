package com.cowell.iscm.service;

import com.cowell.iscm.service.dto.TokenUserDTO;
import com.cowell.iscm.service.dto.registerOrder.RegisterImportErrorDTO;
import com.cowell.iscm.service.dto.registerOrder.RegisterImportDTO;
import com.cowell.iscm.service.dto.registerOrder.RegisterImportRelDTO;
import com.cowell.iscm.service.feign.response.PageResponse;
import org.springframework.http.ResponseEntity;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 近效期登记批导服务接口
 *
 * <AUTHOR>
 */
public interface RegisterImportService {
    /**
     * 批量导入
     *
     * @param file
     * @param userDTO
     * @param importType    1修改问题重新导入 2忽略问题导入剩余数据
     * @return
     * @throws Exception
     */
    List<RegisterImportErrorDTO> batchImport(MultipartFile file, TokenUserDTO userDTO, Integer importType) throws Exception;

    /**
     * 执行保存
     *
     * @param RegisterImportDTOs
     * @return
     */
    int doSave(TokenUserDTO tokenUserDTO, List<RegisterImportDTO> RegisterImportDTOs) throws Exception;

    /**
     * 分页列表
     *
     * @param tokenUserDTO
     * @param page
     * @param pageSize
     * @return
     * @throws Exception
     */
    PageResponse<List<RegisterImportRelDTO>> pageList(TokenUserDTO tokenUserDTO, Integer page, Integer pageSize) throws Exception;

    /**
     * 分页列表(根据状态查询)
     *
     * @param tokenUserDTO
     * @param page
     * @param pageSize
     * @param checkStatusList 状态集合
     * @return
     * @throws Exception
     */
    PageResponse<List<RegisterImportRelDTO>> pageList(TokenUserDTO tokenUserDTO, Integer page, Integer pageSize, List<Byte> checkStatusList) throws Exception;

    /**
     * 逻辑删除
     *
     * @param userDTO
     * @param id
     * @return
     * @throws Exception
     */
    int deleteLogically(TokenUserDTO userDTO, String id) throws Exception;

    /**
     * 导出登记失败的数据
     *
     * @param userDTO
     * @return
     */
    ResponseEntity<byte[]> exportFailedList(TokenUserDTO userDTO) throws Exception;

    /**
     * 导出登记失败的数据
     *
     * @param userDTO
     * @param response
     * @throws Exception
     */
    void exportFailedList(TokenUserDTO userDTO, HttpServletResponse response) throws Exception;
}
