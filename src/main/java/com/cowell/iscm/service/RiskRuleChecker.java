package com.cowell.iscm.service;

import com.cowell.iscm.enums.RiskRuleTypeEnum;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Map;
import java.util.function.Function;

/**
 * Created by schuangxigang on 2022/6/18 21:52.
 */
public class RiskRuleChecker {

    private static final Logger logger = LoggerFactory.getLogger(RiskRuleChecker.class);

    private static final Map<RiskRuleTypeEnum, Function<Object, String>> context = new HashMap<>();

    static {
        context.put(RiskRuleTypeEnum.OVER_NON_APPROVAL_DAYS, RiskRuleChecker::check1);
        context.put(RiskRuleTypeEnum.OVER_POS_DIFFER_COUNT, RiskRuleChecker::checkPositiveInteger);
        context.put(RiskRuleTypeEnum.LOWER_THAN_ISSUE_ROWS, RiskRuleChecker::checkPositiveInteger);

        logger.info("initContext|context={}", context);
    }

    public static String check(RiskRuleTypeEnum riskRuleTypeEnum, Object val) {
        if (riskRuleTypeEnum != null && riskRuleTypeEnum.isChecked()) {
            if (null == val) {
                return "请输入值";
            }
            return context.get(riskRuleTypeEnum).apply(val);
        }
        return "";
    }

    private static String checkInteger(Object val) {
        try {
            Integer.parseInt(val.toString());
        } catch (NumberFormatException e) {
            return "请输入整数";
        }
        return "";
    }

    private static String checkPositiveInteger(Object val) {
        try {
            int i = Integer.parseInt(val.toString());
            if (i <= 0) {
                return "请输入正整数";
            }
        } catch (NumberFormatException e) {
            return "请输入整数";
        }
        return "";
    }

    private static String checkPositiveIntegerAndZero(Object val) {
        try {
            int i = Integer.parseInt(val.toString());
            if (i < 0) {
                return "请输入大于或等于0的整数";
            }
        } catch (NumberFormatException e) {
            return "请输入整数";
        }
        return "";
    }

    private static String check1(Object val) {
        try {
            int i = Integer.parseInt(val.toString());
            if (i > 30 || i < 0) {
                return "请输入0~30之间的整数";
            }
        } catch (NumberFormatException e) {
            return "请输入整数";
        }
        return "";
    }

}
