package com.cowell.iscm.service;

import com.alibaba.fastjson.JSONObject;
import com.cowell.iscm.entity.BdpGoodsGiftDomain;
import com.cowell.iscm.service.dto.TokenUserDTO;
import com.cowell.iscm.service.dto.scib.ScibGoodsGiftDomainDTO;
import com.cowell.iscm.service.dto.scib.ScibGoodsGiftDomainPageDTO;
import com.cowell.iscm.service.dto.scib.ScibGoodsGiftRequestParam;
import com.cowell.iscm.service.feign.response.PageResponse;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * 大仓分货赠品关系维护
 * <AUTHOR>
 * @date  2021-08-27 10:35:07
 */
public interface ScibGoodsGiftService {
    /**
     * 查询赠品关系列表
     * @param userDTO
     * @param param
     * @return
     * @throws Exception
     */
    PageResponse<List<ScibGoodsGiftDomainPageDTO>> getGoodsGiftList(TokenUserDTO userDTO, ScibGoodsGiftRequestParam param) throws Exception;

    /**
     * 查询赠送关系详情
     * @param userDTO
     * @param id
     * @param giftType
     * @return
     */
    ScibGoodsGiftDomainDTO getGoodsGift(TokenUserDTO userDTO, Long id, Byte giftType);

    /**
     * 添加赠品关系列表
     * @param userDTO
     * @param param
     * @return
     * @throws Exception
     */
    Object saveGoodsGiftDomain(TokenUserDTO userDTO, ScibGoodsGiftDomainDTO param) throws Exception;

    /**
     * 更新赠品关系列表
     * @param userDTO
     * @param param
     * @return
     * @throws Exception
     */
    Object updateGoodsGiftDomain(TokenUserDTO userDTO, ScibGoodsGiftDomainDTO param) throws Exception;

    /**
     * 导入
     * @param file
     * @param giftType
     * @param userDTO
     * @return
     * @throws Exception
     */
    JSONObject doImport(MultipartFile file, Byte giftType, TokenUserDTO userDTO) throws Exception;

    /**
     * 导出
     * @param userDTO
     * @param response
     */
    void  doExport(TokenUserDTO userDTO, HttpServletResponse response,ScibGoodsGiftRequestParam param) throws Exception;

    /**
     * 删除
     * @param userDTO
     * @param ids
     * @return
     * @throws Exception
     */
    Boolean deleteGoodsGiftDomain(TokenUserDTO userDTO, List<Long> ids, Byte giftType) throws Exception;

    JSONObject getGoodsGiftToPush(TokenUserDTO userDTO, Long id, Byte giftType);

    CompletableFuture<String> pushGoodsGift(TokenUserDTO userDTO, Long id,Byte giftType);

    CompletableFuture<String> pushGoodsGift(TokenUserDTO userDTO, ScibGoodsGiftDomainDTO domainDTO);
}
