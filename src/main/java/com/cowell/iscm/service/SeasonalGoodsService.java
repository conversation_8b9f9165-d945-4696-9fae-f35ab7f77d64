package com.cowell.iscm.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.cowell.iscm.entity.IscmSeasonalGoods;
import com.cowell.iscm.entity.IscmSeasonalGoodsCompany;
import com.cowell.iscm.entity.IscmSeasonalGoodsPlatform;
import com.cowell.iscm.entity.IscmSeasonalGoodsResp;
import com.cowell.iscm.service.dto.ImportExcelResult;
import com.cowell.iscm.service.dto.applyParam.*;
import com.cowell.iscm.service.feign.response.PageResponse;
import com.cowell.iscm.service.dto.TokenUserDTO;
import org.springframework.web.multipart.MultipartFile;

import java.text.ParseException;
import java.util.Date;
import java.util.List;

public interface SeasonalGoodsService {

    ResponseAmis getSeasonalList(TokenUserDTO userDTO, SeasonalGoodsQuery query) throws Exception;

    JSONObject saveSeasonalGoodsList(TokenUserDTO userDTO, SeasonalGoodsAdd param) throws Exception;

    ResponseAmis batchImportSeasonalGoods(TokenUserDTO userDTO, Long orgId, MultipartFile file, int replaceFlag, String uuId) throws Exception;

    PageResponse<List<IscmSeasonalGoodsResp>> batchImportEditSeasonalGoods(TokenUserDTO userDTO, Long orgId, MultipartFile file, String uuId) throws Exception;

    void deleteSeasonalGoodsByIds(TokenUserDTO userDTO, SeasonalGoodsDelete param) throws Exception;

    void pushSeasonalGoods(Date date);

    void pushSeasonalAddGoods(Date date);

    String getSeasonalGoodsAddUUID();

    PageResponse<List<IscmSeasonalGoodsResp>> addEditSeasonalGoods(TokenUserDTO userDTO, SeasonalGoodsEditAdd param);

    PageResponse<List<IscmSeasonalGoodsResp>> getEditSeasonalGoodsList(TokenUserDTO userDTO, SeasonalGoodsEditQuery param);

    void delEditSeasonalGoods(TokenUserDTO userDTO, SeasonalGoodsEditAdd param);

    void exportAllByOrgId(TokenUserDTO userDTO, Long orgId) throws Exception;

    Byte calSpanYearFlag(String seasonStartMD, String seasonEndMD);

    boolean treatCompanyRepeatFlag(List<IscmSeasonalGoodsCompany> savedList, int repeatFlag,
                                   String goodsNo, String newSeasonStratMD, String newSeasonEndMD) throws ParseException;

    boolean treatPlatformRepeatFlag(List<IscmSeasonalGoodsPlatform> savedList, int repeatFlag,
                                    String goodsNo, String newSeasonStratMD, String newSeasonEndMD) throws ParseException;
}
