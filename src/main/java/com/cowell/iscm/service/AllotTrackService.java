package com.cowell.iscm.service;

import com.cowell.iscm.service.dto.TokenUserDTO;
import com.cowell.iscm.service.dto.visualcenter.allottrack.*;
import com.cowell.iscm.service.feign.response.PageResponse;

import java.util.Date;
import java.util.List;

/**
 * 调拨跟踪服务接口
 * <AUTHOR>
 * @date  2021-03-24 16:36:26
 */
public interface AllotTrackService {
    /**
     * 分页获取登记数据列表
     *
     * @param tokenUserDTO
     * @param trackQuery
     * @return
     * @throws Exception
     */
    PageResponse<List<RegisterDTO>> pageRegisterList(TokenUserDTO tokenUserDTO, TrackQuery trackQuery) throws Exception;

    /**
     * 分页获取建议数据列表
     *
     * @param tokenUserDTO
     * @param trackQuery
     * @return
     * @throws Exception
     */
    PageResponse<List<SuggestDTO>> pageSuggestList(TokenUserDTO tokenUserDTO, TrackQuery trackQuery) throws Exception;

    /**
     * 分页获取POS处理建议未达成列表( 0:未审批 / 1:已审批 / 2:已拒绝)
     *
     * @param tokenUserDTO
     * @param trackQuery
     * @return
     * @throws Exception
     */
    PageResponse<List<ExecuteDTO>> pageSuggestExecutedNotAchieveList(TokenUserDTO tokenUserDTO, TrackQuery trackQuery) throws Exception;


    /**
     * 分页获取POS处理建议已完成列表
     *
     * @param tokenUserDTO
     * @param trackQuery
     * @return
     * @throws Exception
     */
    PageResponse<List<ExecuteDoneDTO>> pageSuggestExecutedList(TokenUserDTO tokenUserDTO, TrackQuery trackQuery) throws Exception;


}
