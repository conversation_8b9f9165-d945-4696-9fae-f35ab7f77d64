package com.cowell.iscm.service;

import com.cowell.iscm.service.dto.TokenUserDTO;
import com.cowell.permission.vo.OrgTreeVO;

import java.util.List;

public interface ThirdService {

    /**
     * 查询指定机构下 指定跟类型下级机构树  默认查询类型 100,300,500,700,800
     * @param orgId
     * @param orgType
     * @param userDTO
     * @return
     * @throws Exception
     */
    OrgTreeVO getUserDataScopeTreesByOrgIdAndType(Long orgId, Integer orgType, TokenUserDTO userDTO) throws Exception;

    /**
     * 查询指定机构下 指定跟类型下级机构树  自定义传入查询类型列表
     * @param orgId
     * @param orgTypeList
     * @param rootType
     * @param userDTO
     * @return
     * @throws Exception
     */
    OrgTreeVO getUserDataScopeTreesByOrgIdAndTypeList(Long orgId, List<Integer> orgTypeList, Integer rootType, TokenUserDTO userDTO) throws Exception;
}
