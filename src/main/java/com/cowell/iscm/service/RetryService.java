package com.cowell.iscm.service;

import com.cowell.iscm.service.feign.dto.StockGoodsBatchCodeSimpleInfo;
import com.cowell.iscm.service.feign.dto.StockGoodsPagableQueryParam;
import com.cowell.permission.vo.OrgVO;

import java.util.List;
import java.util.Map;

public interface RetryService {

    Map<String, List<StockGoodsBatchCodeSimpleInfo>> retryStockGoodsPage(StockGoodsPagableQueryParam param);

    Map<String, List<StockGoodsBatchCodeSimpleInfo>> recoverStockGoodsPage(StockGoodsPagableQueryParam param);

    List<OrgVO> retryQueryOrgInfoBySapCodes(List<String> sapCodes, int type);

    List<OrgVO> recoverQueryOrgInfoBySapCodes(List<String> sapCodes, int type);
}
