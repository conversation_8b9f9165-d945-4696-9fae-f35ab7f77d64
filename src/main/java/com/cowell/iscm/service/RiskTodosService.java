package com.cowell.iscm.service;

import com.cowell.iscm.entity.IscmSuggestAllotGoodsDetail;
import com.cowell.iscm.service.dto.TokenUserDTO;
import com.cowell.iscm.service.dto.risk.RiskTodosDTO;

import java.util.List;
import java.util.Map;

/**
 * 预警待办事项服务接口
 *
 * <AUTHOR>
 */
public interface RiskTodosService {
    /**
     * 查询待办事项列表
     *
     * @param userDTO
     * @param todosType
     * @return
     * @throws Exception
     */
    List<RiskTodosDTO> list(TokenUserDTO userDTO, Byte todosType) throws Exception;

    /**
     * 统计待办数量
     *
     * @param userDTO
     * @return
     */
    Map<String, Long> count(TokenUserDTO userDTO) throws Exception;

    /**
     * 查询待办事项列表
     *
     * @param userDTO
     * @return
     * @throws Exception
     */
    List<RiskTodosDTO> list(TokenUserDTO userDTO) throws Exception;

    /**
     * 操作调拨建议待办
     *
     * @param iscmSuggestAllotGoodsDetail
     * @return
     */
    int handleOperateAllotSuggestEvent(IscmSuggestAllotGoodsDetail iscmSuggestAllotGoodsDetail);
}
