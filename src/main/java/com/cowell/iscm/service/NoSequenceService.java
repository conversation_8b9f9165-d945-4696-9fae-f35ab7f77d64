package com.cowell.iscm.service;

import com.cowell.iscm.enums.OrderTypeEnum;

public interface NoSequenceService {

    /** 生成调拨单单号
     * @param type 单号类型
     * @param storeCode 门店编码
     * @return
     */
    String registerOrderNoSequence(String type, String storeCode);

    /**
     * 门店自主调拨生成调拨单号
     * @param type
     * @param storeCode
     * @return
     */
    String autonomyAllotNoSequence(String type, String storeCode);

    /**
     * 退仓执行单号
     * @param type
     * @param warehouseCode
     * @param goodsNo
     * @return
     */
    String returnWarehouseNo(String type, String warehouseCode, String goodsNo);

    /**
     * 单号生成
     *
     * @param orderTypeEnum
     * @return
     */
    String orderNoSequence(OrderTypeEnum orderTypeEnum);

    /** 生成履约单号
     * @param type 单号类型
     * @param storeCode 门店编码
     * @return
     */
    String performOrderNoSequence(Integer type, String storeCode);

}
