package com.cowell.iscm.service;

import com.cowell.iscm.entity.IscmStoreReturnConfirmOrder;
import com.cowell.iscm.service.dto.CommonProcessDTO;
import com.cowell.iscm.service.dto.TokenUserDTO;
import com.cowell.iscm.service.dto.returnWarehouse.*;
import com.cowell.iscm.service.feign.response.PageResponse;

import java.util.List;
import java.util.Set;

public interface ReturnWarehouseService {

    Integer getReturnDay();

    String fillWarehouseInfo(TokenUserDTO userDTO, Integer registerMonth, String startDate, String endDate) throws Exception;

    List<WarehouseDTO> getWarehouseInfos(Integer registerMonth, Long platformOrgId);

    CommonProcessDTO<PageResponse<List<StoreReturnWarehouseConfirmDTO>>> getStoreReturnWarehouseConfirmList(TokenUserDTO userDTO, ReturnWarehouseParam param) throws Exception;

    List<String> getDistrTypes() throws Exception;

    String autoGenStoreReturnWarehouseConfirmDetail(TokenUserDTO userDTO, ReturnWarehouseDetailParam param) throws Exception;

    void asnyGenStoreReturnOrder(GenReturnOrderParam param) throws Exception;

    PageResponse<List<StoreReturnConfirmOrderDTO>> getStoreReturnWarehouseConfirmOrder(TokenUserDTO userDTO, UpdateConfirmHeadDTO param) throws Exception;

    List<Long> autoChooseConfirmDetail(Long userId, AutoChooseParam param) throws Exception;

    String updateReturnQuantity(TokenUserDTO userDTO, UpdateReturnQuanParam param);

    String fillGoodsClass(TokenUserDTO userDTO, Integer registerMonth, String startDate, String endDate) throws Exception;

    String fillPlatform(TokenUserDTO userDTO, Integer registerMonth, String startDate, String endDate) throws Exception;

    CommonProcessDTO getGenReturnWarehouseProcess(TokenUserDTO userDTO, Long platformOrgId) throws Exception;

    String genReturnWarehoueExec(TokenUserDTO userDTO, GenExecParam param) throws Exception;

    String pushMB(TokenUserDTO userDTO, String goodsNo, String warehouseCode);

    String fillPurChannel(TokenUserDTO userDTO, Integer registerMonth, String startDate, String endDate) throws Exception;

    boolean deleteRedisCache(Long platformOrgId, Long userId) throws Exception;

    String deleteReturnOrder(List<String> returnOrderNos) throws Exception;

    String fillWarehouseInfoByDate(String dateStr) throws Exception;

    CommonProcessDTO<PageResponse<List<StoreReturnWarehouseConfirmDTO>>> getChooseSelfStoreReturnWarehouseConfirmList(TokenUserDTO userDTO, SelfReturnWarehouseParam param) throws Exception;

    void asnyGenStoreReturnOrderDetail(GenReturnOrderDetailParam paramDTO) throws Exception;

    PageResponse<IscmStoreReturnConfirmOrderDetailCountDTO> getStoreReturnWarehouseConfirmOrderDetail(TokenUserDTO userDTO, ConfirmDetailParam param) throws Exception;

    String autoDeleteReturnOrderLessThanToday() throws Exception;

    String batchGenReturnWarehoueExec(TokenUserDTO userDTO, BatchGenExecParam param) throws Exception;

    void asyncGenStoreReturnExecOrder(IscmStoreReturnConfirmOrder order) throws Exception;

    CommonProcessDTO getBatchGenReturnWarehoueExecProcess(TokenUserDTO userDTO, Long platformOrgId) throws Exception;

    Boolean deleteExecRedisCache(Long platformOrgId, Long userId);

    void batchGenStoreReturnWarehouseConfirm(TokenUserDTO userDTO, ReturnWarehouseParam param) throws Exception;

    void batchGenSelfChooseGoodsReturnWarehouseConfirm(TokenUserDTO userDTO, SelfReturnWarehouseParam param) throws Exception;

    StoreReturnConfirmOrderTotalDTO getAllTotalReturnWarehouseConfirmOrder(TokenUserDTO userDTO, UpdateConfirmHeadDTO param);

    CommonProcessDTO<PageResponse<List<StoreReturnWarehouseConfirmDTO>>> getReturnWarehouseConfirmListProcess(String key);
}
