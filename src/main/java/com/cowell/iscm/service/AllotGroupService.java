package com.cowell.iscm.service;

import com.cowell.iscm.service.dto.TokenUserDTO;
import com.cowell.iscm.service.dto.allotGroup.*;
import com.cowell.iscm.service.feign.response.PageResponse;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * Created by schuangxigang on 2022/5/26 18:39.
 */
public interface AllotGroupService {
    PageResponse<List<AllotGroupInfo>> pageGroupList(TokenUserDTO userDTO, AllotGroupQuery allotGroupQuery) throws Exception;

    AllotGroupDetail getGroupDetail(Long id);

    List<AllotGroupSimpleInfo> getGroupList(TokenUserDTO userDTO, String keyword) throws Exception;

    List<AllotGroupStoreSimpleDTO> getStoreList(TokenUserDTO userDTO, String groupCode);

    List<AllotGroupStoreDTO> getStoreOrgList(TokenUserDTO userDTO, AllotStoreQuery query);

    void addGroup(TokenUserDTO userDTO, AllotGroupEdit allotGroupEdit);

    void updateGroup(TokenUserDTO userDTO, AllotGroupEdit allotGroupEdit);

    List<AllotGroupStoreTree> getStoreTrees(TokenUserDTO userDTO, Byte allotType);

    void importGroups(TokenUserDTO userDTO, MultipartFile file) throws Exception;

    void refreshGroupStores();

    AllotGroupInfo getGroupByCode(String groupCode);
}
