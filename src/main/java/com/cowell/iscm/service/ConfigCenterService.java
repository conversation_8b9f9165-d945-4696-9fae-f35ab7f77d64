package com.cowell.iscm.service;

import com.cowell.iscm.service.dto.TokenUserDTO;
import com.cowell.iscm.service.dto.applyParam.DropDownBoxDTO;
import com.cowell.iscm.service.dto.configcenter.*;
import com.cowell.iscm.service.feign.dto.ParamCopyRequest;
import com.cowell.iscm.service.feign.response.PageResponse;
import com.cowell.permission.dto.OrgDTO;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface ConfigCenterService {

    /**
     * 导入参数配置
     * @param file 文件
     * @param userDTO 用户token
     * @return 错误信息
     * @throws Exception 异常信息
     */
    List<ErrorImportParamConfigDTO> configListImport(MultipartFile file, TokenUserDTO userDTO) throws Exception;

    /**
     * 导入参数配置权限
     * @param file 文件
     * @param userDTO 用户token
     * @return 错误信息
     * @throws Exception 异常信息
     */
    List<ErrorImportParamPermDTO> paramPermListImport(MultipartFile file, TokenUserDTO userDTO) throws Exception;

    /**
     * 导入参数商品分类
     * @param file
     * @param userDTO
     * @return 错误信息
     * @throws Exception 异常信息
     */
    List<ErrorImportParamGoodsClassDTO> paramGoodsClassListImport(MultipartFile file, TokenUserDTO userDTO) throws Exception;

    /**
     * 导入参数商品
     * @param file
     * @param userDTO
     * @return
     * @throws Exception
     */
    List<ErrorImportParamGoodsInfoDTO> paramGoodsInfoListImport(MultipartFile file, TokenUserDTO userDTO) throws Exception;

    /**
     * 导入多版本模型
     * @param file
     * @param userDTO
     * @return
     * @throws Exception
     */
    List<ErrorImportMultiModelDTO> modelListImport(MultipartFile file, TokenUserDTO userDTO) throws Exception;

    /**
     * 导入多版本模型权限
     * @param file
     * @param userDTO
     * @return
     * @throws Exception
     */
    List<ErrorImportMulitPermDTO> modelPermListImport(MultipartFile file, TokenUserDTO userDTO) throws Exception;

    /**
     * 获取参数列表
     * @param orgId
     * @param userDTO
     * @return
     * @throws Exception
     */
    OrgParamResponseDTO getOrgConfigs(Long orgId, Byte paramScope, TokenUserDTO userDTO) throws Exception;

    String updateOrgConfigs(Map<String, String> updateParam, TokenUserDTO userDTO) throws Exception;

    String updateParamExtendInfo(UpdateParamExtendRequest updateParam, TokenUserDTO userDTO) throws Exception;

    PageResponse<GoodsInfoResponse> getParamGoodsInfos(OrgParamRequest queryParam, TokenUserDTO userDTO) throws Exception;

    PageResponse<StoreInfoResponse> getParamStoreInfos(OrgParamRequest queryParam, TokenUserDTO userDTO) throws Exception;

    PageResponse<List<IscmParamOrgUpdateRecordDTO>> getParamUpdateRecords(ParamUpdateRecordParam queryParam, TokenUserDTO userDTO) throws Exception;

    List<Long> getParamGoodsClasses(OrgParamRequest queryParam, TokenUserDTO userDTO) throws Exception;

    void updateParamInfo(String paramCode, Integer paramSeq, Integer paramOrder, String paramName, String paramDesc, String paramSequenceDesc) throws Exception;

    void exportStoreGoodsInfo(Long orgId, String paramUniqueCode, TokenUserDTO userDTO, HttpServletResponse response) throws Exception;

    String copyParam(ParamCopyRequest paramRequest, TokenUserDTO userDTO) throws Exception;

    List<ErrorImportParamGoodsInfoDTO> goodsinfoByParamUniqueMark(MultipartFile file, String paramUniqueMark, TokenUserDTO userDTO) throws Exception;

    PageResponse<List<IscmParamConfigCenterDTO>> getConfigList(Byte paramScope, String paramCode, String paramName, Integer page, Integer pageSize);

    void saveConfig(ImportParamConfigDTO param, TokenUserDTO userDTO);

    List<ErrorImportParamPermDTO> saveConfigPerm(SaveConfigPermDTO param, TokenUserDTO userDTO) throws Exception;

    List<DropDownBoxDTO> dropDownBoxList(Integer type);

    IscmParamConfigCenterDTO getConfigInfo(String id);

    /**
     *
     * @param importDTOS
     * @param orgMap
     * @param orgToBdpMap
     * @param userDTO
     * @param faultTolerant 是否容错
     * @return
     * @throws Exception
     */
    List<ErrorImportParamPermDTO> savePermParam(List<ImportParamPermDTO> importDTOS, Map<Long, OrgDTO> orgMap, Map<Long, OrgToBdpMapping> orgToBdpMap, UserExDTO userDTO, Boolean faultTolerant) throws Exception;

    List<IscmParamOrgManagementDTO> getOrgParamList(QueryOrgParam param);
}
