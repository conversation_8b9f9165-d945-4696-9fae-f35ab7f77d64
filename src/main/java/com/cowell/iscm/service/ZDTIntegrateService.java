package com.cowell.iscm.service;

import com.cowell.iscm.entity.IscmSuggestAllotGoodsDetail;
import com.cowell.iscm.service.dto.TokenUserDTO;
import com.cowell.iscm.service.dto.zdt.ZdtPosApproveDTO;

import java.util.List;
import java.util.Map;

public interface ZDTIntegrateService {
    void pushWillPosApproveOrder(TokenUserDTO userDTO, Boolean createTaskAble) throws Exception ;


    /**
     *
     * @param userDTO
     * @param taskId
     * @return true 调入 false 调出
     */
    ZdtPosApproveDTO viewPosApproveOrder(TokenUserDTO userDTO, Long taskId);

    void pushWillApproveOrder(TokenUserDTO userDTO, Boolean createTaskAble) throws Exception;

    /**
     * 更新待审批任务的关系表
     * @param iscmSuggestAllotGoodsDetails
     * @throws Exception
     */
    void updateWillApproveTaskRelation(List<IscmSuggestAllotGoodsDetail> iscmSuggestAllotGoodsDetails) throws Exception;

    Map viewWillApproveOrder(TokenUserDTO userDTO, Long taskId) throws Exception;

    void deleteWillTask() throws Exception;
}
