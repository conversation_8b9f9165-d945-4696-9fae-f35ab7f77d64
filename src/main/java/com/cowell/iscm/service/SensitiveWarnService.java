package com.cowell.iscm.service;

import com.cowell.iscm.service.dto.TocCallbackParam;
import com.cowell.iscm.service.dto.TokenUserDTO;
import com.cowell.iscm.service.dto.controlTower.ConfigNameDTO;
import com.cowell.iscm.service.dto.sensitive.SensitiveWarnSaveDTO;

import java.util.List;

public interface SensitiveWarnService {

    String saveSensitiveConfig(SensitiveWarnSaveDTO save, TokenUserDTO userDTO) throws Exception;

    List<ConfigNameDTO> getConfigNameList(Long orgId, TokenUserDTO userDTO);

    SensitiveWarnSaveDTO getSensitivewarnConfig(String paramUniqueMark);

//    // 创建提醒任务
//    void createTowerWarnJob();
//
//    void genWarnData(TocCallbackParam param);
//
//    void consumeControlTowerMsg(TocCallbackParam param);
}
