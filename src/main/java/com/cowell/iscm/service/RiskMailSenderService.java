package com.cowell.iscm.service;

import com.cowell.iscm.entity.IscmRiskConfig;
import com.cowell.iscm.enums.RiskEnableMailNotifierEnum;
import com.cowell.iscm.enums.RiskMailSendResultEnum;
import com.cowell.iscm.mapper.IscmRiskConfigMapper;
import com.cowell.iscm.service.dto.risk.RiskConfigDTO;
import com.cowell.iscm.utils.DateUtils;
import com.cowell.iscm.utils.context.SpringContextUtil;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Date;
import java.util.Objects;

/**
 * 预警邮件发送接口
 *
 * Created by schuangxigang on 2022/6/16 14:42.
 */
public interface RiskMailSenderService {
    RiskMailSendResultEnum sendMail(Long riskId);
    RiskMailSendResultEnum sendMail(RiskConfigDTO riskConfigDTO);

    Logger LOGGER = LoggerFactory.getLogger(RiskMailSenderService.class);

    IscmRiskConfigMapper iscmRiskConfigMapper = SpringContextUtil.getBean(IscmRiskConfigMapper.class);

    default boolean checkSendMail(RiskConfigDTO riskConfigDTO) {
        if (riskConfigDTO.getEnableMailNotifier() == null
                || riskConfigDTO.getEnableMailNotifier() != RiskEnableMailNotifierEnum.Y.getCode()) {
            LOGGER.warn("邮件提醒功能未开启 <== riskId={}", riskConfigDTO.getId());
            return false;
        }
        if (CollectionUtils.isEmpty(riskConfigDTO.getEmails())) {
            LOGGER.warn("预警邮箱配置为空 <== riskId={}", riskConfigDTO.getId());
            return false;
        }
        if (isTodaySendOk(riskConfigDTO.getMailSendTime(), riskConfigDTO.getMailSendResult())) {
            // 当天已经发送成功的不发
            LOGGER.warn("当天已经发送过预警邮件 <== riskId={}", riskConfigDTO.getId());
            return false;
        }
        return true;
    }

    default int updateMailSendResult(String riskId, RiskMailSendResultEnum riskMailSendResultEnum) {
        IscmRiskConfig iscmRiskConfig = iscmRiskConfigMapper.selectByPrimaryKey(Long.valueOf(riskId));
        if (iscmRiskConfig != null && !isTodaySendOk(iscmRiskConfig.getMailSendTime(), iscmRiskConfig.getMailSendResult())) {
            IscmRiskConfig record = new IscmRiskConfig();
            record.setId(Long.valueOf(riskId));
            record.setMailSendTime(new Date());
            record.setMailSendResult(riskMailSendResultEnum.getCode());
            record.setGmtUpdate(iscmRiskConfig.getGmtUpdate());
            return iscmRiskConfigMapper.updateByPrimaryKeySelective(record);
        }
        return 0;
    }

    default boolean isTodaySendOk(Date sendTime, Byte sendResult) {
        boolean isToday = DateUtils.conventDateStrByPattern(new Date(), "yyyyMMdd").equals(
                DateUtils.conventDateStrByPattern(sendTime, "yyyyMMdd"));
        return isToday && Objects.equals(sendResult, RiskMailSendResultEnum.SEND_OK.getCode());
    }
}
