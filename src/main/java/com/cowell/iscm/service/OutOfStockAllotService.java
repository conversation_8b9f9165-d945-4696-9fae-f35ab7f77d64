package com.cowell.iscm.service;

import com.cowell.iscm.service.dto.allotextend.*;

import java.util.List;

public interface OutOfStockAllotService {
    List<AllotGoodsDTO> judgeGoodsAllot(GoodsJudgeDTO param);

    void allotExecute(AllotExecDTO param);

    void asnyAllotExecute(AllotExecDTO param) throws Exception;

    AllotDoneDTO genDoneDTO(String salesOrderNo, String iscmAllotNo, List<AllotDoneGoodsDetailDTO> detailDTOS, Integer execStatus, String errorMsg);

    void managePosTransferApproveSuggest(String msg);

    void judgeStoreAllot(Long inStoreId, Long outStoreId, Boolean createInAble);
}
