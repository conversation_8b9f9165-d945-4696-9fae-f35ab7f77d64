package com.cowell.iscm.service;

import com.codahale.metrics.annotation.Timed;
import com.cowell.iscm.service.feign.dto.FileDownloadTaskDTO;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

/**
 * =======================================================
 *
 * @Company 高济医疗
 * @Date ：2019-12-24 17:21
 * <AUTHOR>
 * @Version ：0.0.1
 * @Description =======================================================
 */
@FeignClient(name = "scrm")
public interface ScrmFeignService {

    @RequestMapping(value = "/api/noauth/fileDownloadTask", method = RequestMethod.POST)
    @Timed
    @ApiOperation(value = "新建文件下载任务")
    FileDownloadTaskDTO createFileDownloadTask(@RequestBody FileDownloadTaskDTO fileDownloadTaskDTO);

    @RequestMapping(value = "/api/noauth/fileDownloadTask", method = RequestMethod.PUT)
    @Timed
    @ApiOperation(value = "更新文件下载任务 ")
    FileDownloadTaskDTO updateFileDownloadTask(@RequestBody FileDownloadTaskDTO fileDownloadTaskDTO);
}
