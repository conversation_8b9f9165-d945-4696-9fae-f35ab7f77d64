package com.cowell.iscm.service;

import com.cowell.iscm.enums.RiskMailSendResultEnum;
import com.cowell.iscm.enums.RiskTypeEnum;
import com.cowell.iscm.rest.errors.BusinessErrorException;
import com.cowell.iscm.service.dto.risk.RiskConfigDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.DependsOn;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

/**
 * Created by schuangxigang on 2022/6/18 21:52.
 */
@DependsOn("springContextUtil")
@Service
public class RiskMailSenderContext {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    private final Map<RiskTypeEnum, RiskMailSenderService> contextMap = new HashMap<>();

    @Qualifier("riskMailAllotSuggestApprovalSenderService")
    @Autowired
    private RiskMailSenderService riskMailAllotSuggestApprovalSenderService;

    @Qualifier("riskMailDailySuggestMonitorSenderService")
    @Autowired
    private RiskMailSenderService riskMailDailySuggestMonitorSenderService;

    @PostConstruct
    public void initContext() {
        contextMap.put(RiskTypeEnum.STORE_AUTO_ALLOT_PROPOSAL_APPROVAL, riskMailAllotSuggestApprovalSenderService);
        contextMap.put(RiskTypeEnum.STORE_REPLENISHMENT_MONITOR_DIFFER, riskMailDailySuggestMonitorSenderService);

        logger.info("<== initContext|contextMap={}", contextMap);
    }

    public RiskMailSendResultEnum sendMail(RiskConfigDTO riskConfigDTO, RiskTypeEnum riskTypeEnum) {
        Optional.ofNullable(riskTypeEnum).orElseThrow(() -> new BusinessErrorException("riskTypeEnum must not be null."));
        logger.info("<== RiskMailSenderService|{}={}", riskTypeEnum, contextMap.get(riskTypeEnum));
        return Optional.ofNullable(contextMap.get(riskTypeEnum)).orElseThrow(() -> new BusinessErrorException("["+riskTypeEnum.getName()+"]未注册预警邮件的接口")).sendMail(riskConfigDTO);
    }

}
