package com.cowell.iscm.service;

import com.cowell.iscm.enums.OrgTypeEnum;
import com.cowell.iscm.service.feign.dto.SpuListVo;
import com.cowell.iscm.service.feign.dto.StockGoodsCountInfo;
import com.cowell.permission.dto.OrgDTO;
import com.cowell.permission.dto.mdm.MdmStoreBaseDTO;
import com.cowell.permission.vo.OrgVO;

import java.util.List;
import java.util.Map;

/**
 * ISCM辅助服务接口
 *
 * <AUTHOR>
 */
public interface IscmAuxService {
    /**
     * 获取门店信息Map
     *
     * @param storeCodes 门店编码列表
     * @return
     * @throws Exception
     */
    Map<String, MdmStoreBaseDTO> getStoreMap(List<String> storeCodes) throws Exception;

    /**
     * 获取组织信息Map (废弃 同一个orgCode 有可能会匹配多条记录  结果只取了最后一条 存在数据错误风险 )
     * 建议使用   Map<String, OrgVO> getOrgMap(List<String> orgCodes,OrgTypeEnum orgType) 替换
     * 例如 测试环境 3080 和 A212
     * @param orgCodes 组织编码列表
     * @return
     * @throws Exception
     */
    @Deprecated
    Map<String, OrgVO> getOrgMap(List<String> orgCodes) throws Exception;

    /**
     * 获取组织信息Map 指定级别
     *
     * @param orgCodes 组织编码列表
     * @return
     * @throws Exception
     */
    Map<String, OrgVO> getOrgMap(List<String> orgCodes,OrgTypeEnum orgType) throws Exception;

    /**
     * 获取商品信息Map
     *
     * @param goodsNos 商品编号列表
     * @return
     * @throws Exception
     */
    Map<String, SpuListVo> getGoodsMap(List<String> goodsNos) throws Exception;

//    /**
//     * 获取库存信息Map
//     *
//     * @param storeMap      门店编码 - 门店详细信息
//     * @param storeGoodsMap 门店编码 - 商品编号列表
//     * @return
//     * @throws Exception
//     */
//    Map<String, List<StockGoodsCountInfo>> getStockMap(Map<String, MdmStoreBaseDTO> storeMap, Map<String, List<String>> storeGoodsMap) throws Exception;

    /**
     * 获取库存信息Map
     *
     * @param storeGoodsMap 门店编码 - 商品编号列表
     * @return
     * @throws Exception
     */
    Map<String, List<StockGoodsCountInfo>> getStockMap(Map<String, List<String>> storeGoodsMap) throws Exception;

    /**
     * 获取库存信息Map
     *
     * @param storeGoodsMap 门店编码 - 商品编号列表
     * @return
     * @throws Exception
     */
    Map<String, List<StockGoodsCountInfo>> getStockMapGroupingByStoreCodeAndGoodsNo(Map<String, List<String>> storeGoodsMap)throws Exception;

    /**
     * 获取库存信息Map
     *
     * @param storeGoodsMap 门店编码 - 商品编号列表
     * @param parentOrgMap  门店编码 - 上级组织权限
     * @return
     * @throws Exception
     */
    Map<String, List<StockGoodsCountInfo>> getStockMap(Map<String, List<String>> storeGoodsMap, Map<String, List<OrgDTO>> parentOrgMap) throws Exception;


    /**
     * 获取库存信息Map
     *
     * @param storeGoodsMap 门店编码 - 商品编号列表
     * @param parentOrgMap  门店编码 - 上级组织权限
     * @return
     * @throws Exception
     */
    public Map<String, List<StockGoodsCountInfo>> getStockMapGroupingByStoreCodeAndGoodsNo(Map<String, List<String>> storeGoodsMap, Map<String, List<OrgDTO>> parentOrgMap) throws Exception;

    /**
     * 根据 orgId/orgType 获取父级组织
     *
     * @param orgId
     * @param orgType
     * @return
     * @throws Exception
     */
    OrgDTO getParentOrgByIdAndType(Long orgId, OrgTypeEnum orgType) throws Exception;

    /**
     * 根据 orgId 获取父级组织(包含本级)
     *
     * @param orgId
     * @return
     * @throws Exception
     */
    List<OrgDTO> getParentOrgListById(Long orgId) throws Exception;

    /**
     * 根据 orgIds/orgType 获取父级组织
     *
     * @param orgIds
     * @param orgType
     * @return
     * @throws Exception
     */
    Map<Long, OrgDTO> getParentOrgMapByIdListAndType(List<Long> orgIds, OrgTypeEnum orgType) throws Exception;

    /**
     * 根据 orgIds 获取父级组织(包含本级)
     *
     * @param orgIds
     * @return
     * @throws Exception
     */
    Map<Long, List<OrgDTO>> getParentOrgMapByIdList(List<Long> orgIds) throws Exception;

    /**
     * 根据 orgCodes/orgType 获取父级组织
     *
     * @param orgCodes 默认为连锁Code
     * @param orgType  返回的组织对应的orgType
     * @return
     * @throws Exception
     */
    Map<String, OrgDTO> getParentOrgMapByCodeListAndType(List<String> orgCodes, OrgTypeEnum orgType) throws Exception;

    /**
     * 根据 orgCodes/orgType 获取父级组织
     *
     * @param orgCodes
     * @param queryOrgType  查询的组织对应的orgType
     * @param returnOrgType 返回的组织对应的orgType
     * @return
     * @throws Exception
     */
    Map<String, OrgDTO> getParentOrgMapByCodeListAndType(List<String> orgCodes, OrgTypeEnum queryOrgType, OrgTypeEnum returnOrgType) throws Exception;

    /**
     * 根据 orgCodes 获取父级组织(包含本级)
     *
     * @param orgCodes 默认为连锁Code
     * @return
     * @throws Exception
     */
    Map<String, List<OrgDTO>> getParentOrgMapByCodeList(List<String> orgCodes) throws Exception;

    /**
     * 根据 orgCodes 获取父级组织(包含本级)
     *
     * @param orgCodes
     * @param queryOrgType  orgCodes对应的orgType
     * @return
     * @throws Exception
     */
    Map<String, List<OrgDTO>> getParentOrgMapByCodeList(List<String> orgCodes, OrgTypeEnum queryOrgType) throws Exception;

//    /**
//     * 根据 门店Code-门店信息/orgType 获取父级组织
//     *
//     * @param storeMap
//     * @param orgType
//     * @return
//     * @throws Exception
//     */
//    Map<String, OrgDTO> getParentOrgMapByStoreMapAndType(Map<String, MdmStoreBaseDTO> storeMap, OrgTypeEnum orgType) throws Exception;
//
//    /**
//     * 根据 门店Code-门店信息 获取父级组织
//     *
//     * @param storeMap
//     * @return
//     * @throws Exception
//     */
//    Map<String, List<OrgDTO>> getParentOrgMapByStoreMap(Map<String, MdmStoreBaseDTO> storeMap) throws Exception;

    /**
     * 根据门店 storeCodes/orgType 获取父级组织
     *
     * @param storeCodes
     * @param orgType
     * @return
     * @throws Exception
     */
    Map<String, OrgDTO> getParentOrgMapByStoreCodeListAndType(List<String> storeCodes, OrgTypeEnum orgType) throws Exception;

    /**
     * 根据 storeCodes 获取父级组织(包含本级)
     *
     * @param storeCodes
     * @return
     * @throws Exception
     */
    Map<String, List<OrgDTO>> getParentOrgMapByStoreCodeList(List<String> storeCodes) throws Exception;

    /**
     * 从所有上级组织中获取对应类型的组织
     *
     * @param parentOrgList
     * @param orgType
     * @return
     */
    OrgDTO getOrgByType(List<OrgDTO> parentOrgList, OrgTypeEnum orgType);

    /**
     * 从所有上级组织中获取对应类型的组织名称
     *
     * @param parentOrgList
     * @param orgType
     * @return
     */
    String getOrgNameByType(List<OrgDTO> parentOrgList, OrgTypeEnum orgType);


    /**
     * 从所有上级组织中获取对应类型的组织简称
     *
     * @param parentOrgList
     * @param orgType
     * @return
     */
    String getOrgShortNameByType(List<OrgDTO> parentOrgList, OrgTypeEnum orgType);
}
