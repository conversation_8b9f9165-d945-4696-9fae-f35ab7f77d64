package com.cowell.iscm.service;

import com.cowell.iscm.entity.IscmJumpUrlContact;
import com.cowell.iscm.service.dto.TokenUserDTO;
import com.cowell.iscm.service.dto.jump.IscmJumpUrlContactDTO;
import com.cowell.iscm.service.feign.response.PageResponse;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * 跳转URL 通用组装能力支持
 * <AUTHOR>
 */
public interface IscmJumpSupportService {
    /**
     * 通过 jumpCode 找到指定跳转Target地址
     * @param request
     * @param jumpCode
     * @return
     */
    String getJumpTarget(HttpServletRequest request,String jumpCode);

    /**
     * 根据条件查询全部记录
     *
     * @param iscmJumpUrlContact
     * @return
     */
    PageResponse<List<IscmJumpUrlContact>> getIscmJumpUrlContactsBySearch(IscmJumpUrlContactDTO iscmJumpUrlContact);

    /**
     * 根据主键获取对应记录
     *
     * @param jumpCode 唯一编码
     * @return
     */
    IscmJumpUrlContactDTO getIscmJumpUrlContactByCode(String jumpCode);

    /**
     * 新增/更新记录(全部字段)
     *
     * @param iscmJumpUrlContact
     * @param userDTO
     * @return
     */
    int saveOrUpdateIscmJumpUrlContact(IscmJumpUrlContactDTO iscmJumpUrlContact, TokenUserDTO userDTO);

    /**
     * 根据主键删除对应记录
     *
     * @param jumpCode 主键
     * @return
     */
    int deleteIscmJumpUrlContactByCode(String jumpCode);
    /**
     * 更新SAP参数信息
     *
     * @param jumpCode 主键
     * @param sapInfo  跳转鉴权字段
     * @param userDTO 用户信息
     * @return
     */

    int updateSapInfoByCode(String jumpCode,String sapInfo,TokenUserDTO userDTO);
}

