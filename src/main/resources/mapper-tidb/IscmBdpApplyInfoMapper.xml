<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.iscm.mapperTidb.IscmBdpApplyInfoMapper">
  <resultMap id="BaseResultMap" type="com.cowell.iscm.entityTidb.IscmBdpApplyInfo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="apply_no" jdbcType="VARCHAR" property="applyNo" />
    <result column="apply_line" jdbcType="VARCHAR" property="applyLine" />
    <result column="company_code" jdbcType="VARCHAR" property="companyCode" />
    <result column="store_code" jdbcType="VARCHAR" property="storeCode" />
    <result column="apply_date" jdbcType="DATE" property="applyDate" />
    <result column="data_origin_type" jdbcType="TINYINT" property="dataOriginType" />
    <result column="goods_no" jdbcType="VARCHAR" property="goodsNo" />
    <result column="apply_goods_type" jdbcType="TINYINT" property="applyGoodsType" />
    <result column="apply_total" jdbcType="DECIMAL" property="applyTotal" />
    <result column="unqualified_await_stock" jdbcType="DECIMAL" property="unqualifiedAwaitStock" />
    <result column="transit_stock" jdbcType="DECIMAL" property="transitStock" />
    <result column="stock" jdbcType="DECIMAL" property="stock" />
    <result column="lock_stock" jdbcType="DECIMAL" property="lockStock" />
    <result column="unqualified_stock" jdbcType="DECIMAL" property="unqualifiedStock" />
    <result column="apply_transit_stock" jdbcType="DECIMAL" property="applyTransitStock" />
    <result column="in_transit_stock" jdbcType="DECIMAL" property="inTransitStock" />
    <result column="distr_transit_stock" jdbcType="DECIMAL" property="distrTransitStock" />
    <result column="special_ctrl" jdbcType="VARCHAR" property="specialCtrl" />
    <result column="special_thirty_days_qty" jdbcType="DECIMAL" property="specialThirtyDaysQty" />
    <result column="goods_level" jdbcType="VARCHAR" property="goodsLevel" />
    <result column="stock_upper_limit_days" jdbcType="INTEGER" property="stockUpperLimitDays" />
    <result column="stock_lower_limit_days" jdbcType="INTEGER" property="stockLowerLimitDays" />
    <result column="bdp_average_daily_sales" jdbcType="DECIMAL" property="bdpAverageDailySales" />
    <result column="min_display_qty" jdbcType="DECIMAL" property="minDisplayQty" />
    <result column="stock_upper_limit" jdbcType="DECIMAL" property="stockUpperLimit" />
    <result column="stock_lower_limit" jdbcType="DECIMAL" property="stockLowerLimit" />
    <result column="buy_stock" jdbcType="DECIMAL" property="buyStock" />
    <result column="sale_days_before" jdbcType="DECIMAL" property="saleDaysBefore" />
    <result column="sale_days_after" jdbcType="DECIMAL" property="saleDaysAfter" />
    <result column="thirty_days_sales" jdbcType="DECIMAL" property="thirtyDaysSales" />
    <result column="ninety_days_sales" jdbcType="DECIMAL" property="ninetyDaysSales" />
    <result column="company_apply_total" jdbcType="BIGINT" property="companyApplyTotal" />
    <result column="store_apply_total" jdbcType="BIGINT" property="storeApplyTotal" />
    <result column="middle_package_switch" jdbcType="VARCHAR" property="middlePackageSwitch" />
    <result column="middle_package_qty" jdbcType="DECIMAL" property="middlePackageQty" />
    <result column="middle_code_flag" jdbcType="TINYINT" property="middleCodeFlag" />
    <result column="apply_ratio" jdbcType="DECIMAL" property="applyRatio" />
    <result column="category_id" jdbcType="BIGINT" property="categoryId" />
    <result column="purchase_type" jdbcType="INTEGER" property="purchaseType" />
    <result column="purchase_channel" jdbcType="VARCHAR" property="purchaseChannel" />
    <result column="warehouse_code" jdbcType="VARCHAR" property="warehouseCode" />
    <result column="recommend_reason" jdbcType="VARCHAR" property="recommendReason" />
    <result column="promotion_name" jdbcType="VARCHAR" property="promotionName" />
    <result column="promotion_way" jdbcType="VARCHAR" property="promotionWay" />
    <result column="threshold_info" jdbcType="VARCHAR" property="thresholdInfo" />
    <result column="fav_info" jdbcType="VARCHAR" property="favInfo" />
    <result column="composite_new" jdbcType="TINYINT" property="compositeNew" />
    <result column="thirty_sales_quantity" jdbcType="DECIMAL" property="thirtySalesQuantity" />
    <result column="promotion_title" jdbcType="VARCHAR" property="promotionTitle" />
    <result column="promotion_start_date" jdbcType="VARCHAR" property="promotionStartDate" />
    <result column="promotion_end_date" jdbcType="VARCHAR" property="promotionEndDate" />
    <result column="deal_suggest" jdbcType="TINYINT" property="dealSuggest" />
    <result column="store_attr" jdbcType="TINYINT" property="storeAttr" />
    <result column="three_days_sales" jdbcType="DECIMAL" property="threeDaysSales" />
    <result column="deliverycycle_code" jdbcType="VARCHAR" property="deliverycycleCode" />
    <result column="new_product" jdbcType="TINYINT" property="newProduct" />
    <result column="goods_cut_type" jdbcType="INTEGER" property="goodsCutType" />
    <result column="lawful" jdbcType="INTEGER" property="lawful" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
    <result column="reason" jdbcType="VARCHAR" property="reason" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, apply_no, apply_line, company_code, store_code, apply_date, data_origin_type, 
    goods_no, apply_goods_type, apply_total, unqualified_await_stock, transit_stock, 
    stock, lock_stock, unqualified_stock, apply_transit_stock, in_transit_stock, distr_transit_stock, 
    special_ctrl, special_thirty_days_qty, goods_level, stock_upper_limit_days, stock_lower_limit_days, 
    bdp_average_daily_sales, min_display_qty, stock_upper_limit, stock_lower_limit, buy_stock, 
    sale_days_before, sale_days_after, thirty_days_sales, ninety_days_sales, company_apply_total, 
    store_apply_total, middle_package_switch, middle_package_qty, middle_code_flag, apply_ratio, 
    category_id, purchase_type, purchase_channel, warehouse_code, recommend_reason, promotion_name, 
    promotion_way, threshold_info, fav_info, composite_new, thirty_sales_quantity, promotion_title, 
    promotion_start_date, promotion_end_date, deal_suggest, store_attr, three_days_sales, 
    deliverycycle_code, new_product, goods_cut_type,lawful, gmt_create, gmt_update, reason
  </sql>
  <select id="selectByExample" parameterType="com.cowell.iscm.entityTidb.IscmBdpApplyInfoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from iscm_bdp_apply_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from iscm_bdp_apply_info
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from iscm_bdp_apply_info
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.iscm.entityTidb.IscmBdpApplyInfoExample">
    delete from iscm_bdp_apply_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.cowell.iscm.entityTidb.IscmBdpApplyInfo" useGeneratedKeys="true">
    insert into iscm_bdp_apply_info (apply_no, apply_line, company_code, 
      store_code, apply_date, data_origin_type, 
      goods_no, apply_goods_type, apply_total, 
      unqualified_await_stock, transit_stock, stock, 
      lock_stock, unqualified_stock, apply_transit_stock, 
      in_transit_stock, distr_transit_stock, special_ctrl, 
      special_thirty_days_qty, goods_level, stock_upper_limit_days, 
      stock_lower_limit_days, bdp_average_daily_sales, 
      min_display_qty, stock_upper_limit, stock_lower_limit, 
      buy_stock, sale_days_before, sale_days_after, 
      thirty_days_sales, ninety_days_sales, company_apply_total, 
      store_apply_total, middle_package_switch, middle_package_qty, 
      middle_code_flag, apply_ratio, category_id, 
      purchase_type, purchase_channel, warehouse_code, 
      recommend_reason, promotion_name, promotion_way, 
      threshold_info, fav_info, composite_new, 
      thirty_sales_quantity, promotion_title, promotion_start_date, 
      promotion_end_date, deal_suggest, store_attr, 
      three_days_sales, deliverycycle_code, new_product, 
      goods_cut_type, gmt_create, gmt_update, 
      reason)
    values (#{applyNo,jdbcType=VARCHAR}, #{applyLine,jdbcType=VARCHAR}, #{companyCode,jdbcType=VARCHAR}, 
      #{storeCode,jdbcType=VARCHAR}, #{applyDate,jdbcType=DATE}, #{dataOriginType,jdbcType=TINYINT}, 
      #{goodsNo,jdbcType=VARCHAR}, #{applyGoodsType,jdbcType=TINYINT}, #{applyTotal,jdbcType=DECIMAL}, 
      #{unqualifiedAwaitStock,jdbcType=DECIMAL}, #{transitStock,jdbcType=DECIMAL}, #{stock,jdbcType=DECIMAL}, 
      #{lockStock,jdbcType=DECIMAL}, #{unqualifiedStock,jdbcType=DECIMAL}, #{applyTransitStock,jdbcType=DECIMAL}, 
      #{inTransitStock,jdbcType=DECIMAL}, #{distrTransitStock,jdbcType=DECIMAL}, #{specialCtrl,jdbcType=VARCHAR}, 
      #{specialThirtyDaysQty,jdbcType=DECIMAL}, #{goodsLevel,jdbcType=VARCHAR}, #{stockUpperLimitDays,jdbcType=INTEGER}, 
      #{stockLowerLimitDays,jdbcType=INTEGER}, #{bdpAverageDailySales,jdbcType=DECIMAL}, 
      #{minDisplayQty,jdbcType=DECIMAL}, #{stockUpperLimit,jdbcType=DECIMAL}, #{stockLowerLimit,jdbcType=DECIMAL}, 
      #{buyStock,jdbcType=DECIMAL}, #{saleDaysBefore,jdbcType=DECIMAL}, #{saleDaysAfter,jdbcType=DECIMAL}, 
      #{thirtyDaysSales,jdbcType=DECIMAL}, #{ninetyDaysSales,jdbcType=DECIMAL}, #{companyApplyTotal,jdbcType=BIGINT}, 
      #{storeApplyTotal,jdbcType=BIGINT}, #{middlePackageSwitch,jdbcType=VARCHAR}, #{middlePackageQty,jdbcType=DECIMAL}, 
      #{middleCodeFlag,jdbcType=TINYINT}, #{applyRatio,jdbcType=DECIMAL}, #{categoryId,jdbcType=BIGINT}, 
      #{purchaseType,jdbcType=INTEGER}, #{purchaseChannel,jdbcType=VARCHAR}, #{warehouseCode,jdbcType=VARCHAR}, 
      #{recommendReason,jdbcType=VARCHAR}, #{promotionName,jdbcType=VARCHAR}, #{promotionWay,jdbcType=VARCHAR}, 
      #{thresholdInfo,jdbcType=VARCHAR}, #{favInfo,jdbcType=VARCHAR}, #{compositeNew,jdbcType=TINYINT}, 
      #{thirtySalesQuantity,jdbcType=DECIMAL}, #{promotionTitle,jdbcType=VARCHAR}, #{promotionStartDate,jdbcType=VARCHAR}, 
      #{promotionEndDate,jdbcType=VARCHAR}, #{dealSuggest,jdbcType=TINYINT}, #{storeAttr,jdbcType=TINYINT}, 
      #{threeDaysSales,jdbcType=DECIMAL}, #{deliverycycleCode,jdbcType=VARCHAR}, #{newProduct,jdbcType=TINYINT}, 
      #{goodsCutType,jdbcType=INTEGER}, #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtUpdate,jdbcType=TIMESTAMP}, 
      #{reason,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.cowell.iscm.entityTidb.IscmBdpApplyInfo" useGeneratedKeys="true">
    insert into iscm_bdp_apply_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="applyNo != null">
        apply_no,
      </if>
      <if test="applyLine != null">
        apply_line,
      </if>
      <if test="companyCode != null">
        company_code,
      </if>
      <if test="storeCode != null">
        store_code,
      </if>
      <if test="applyDate != null">
        apply_date,
      </if>
      <if test="dataOriginType != null">
        data_origin_type,
      </if>
      <if test="goodsNo != null">
        goods_no,
      </if>
      <if test="applyGoodsType != null">
        apply_goods_type,
      </if>
      <if test="applyTotal != null">
        apply_total,
      </if>
      <if test="unqualifiedAwaitStock != null">
        unqualified_await_stock,
      </if>
      <if test="transitStock != null">
        transit_stock,
      </if>
      <if test="stock != null">
        stock,
      </if>
      <if test="lockStock != null">
        lock_stock,
      </if>
      <if test="unqualifiedStock != null">
        unqualified_stock,
      </if>
      <if test="applyTransitStock != null">
        apply_transit_stock,
      </if>
      <if test="inTransitStock != null">
        in_transit_stock,
      </if>
      <if test="distrTransitStock != null">
        distr_transit_stock,
      </if>
      <if test="specialCtrl != null">
        special_ctrl,
      </if>
      <if test="specialThirtyDaysQty != null">
        special_thirty_days_qty,
      </if>
      <if test="goodsLevel != null">
        goods_level,
      </if>
      <if test="stockUpperLimitDays != null">
        stock_upper_limit_days,
      </if>
      <if test="stockLowerLimitDays != null">
        stock_lower_limit_days,
      </if>
      <if test="bdpAverageDailySales != null">
        bdp_average_daily_sales,
      </if>
      <if test="minDisplayQty != null">
        min_display_qty,
      </if>
      <if test="stockUpperLimit != null">
        stock_upper_limit,
      </if>
      <if test="stockLowerLimit != null">
        stock_lower_limit,
      </if>
      <if test="buyStock != null">
        buy_stock,
      </if>
      <if test="saleDaysBefore != null">
        sale_days_before,
      </if>
      <if test="saleDaysAfter != null">
        sale_days_after,
      </if>
      <if test="thirtyDaysSales != null">
        thirty_days_sales,
      </if>
      <if test="ninetyDaysSales != null">
        ninety_days_sales,
      </if>
      <if test="companyApplyTotal != null">
        company_apply_total,
      </if>
      <if test="storeApplyTotal != null">
        store_apply_total,
      </if>
      <if test="middlePackageSwitch != null">
        middle_package_switch,
      </if>
      <if test="middlePackageQty != null">
        middle_package_qty,
      </if>
      <if test="middleCodeFlag != null">
        middle_code_flag,
      </if>
      <if test="applyRatio != null">
        apply_ratio,
      </if>
      <if test="categoryId != null">
        category_id,
      </if>
      <if test="purchaseType != null">
        purchase_type,
      </if>
      <if test="purchaseChannel != null">
        purchase_channel,
      </if>
      <if test="warehouseCode != null">
        warehouse_code,
      </if>
      <if test="recommendReason != null">
        recommend_reason,
      </if>
      <if test="promotionName != null">
        promotion_name,
      </if>
      <if test="promotionWay != null">
        promotion_way,
      </if>
      <if test="thresholdInfo != null">
        threshold_info,
      </if>
      <if test="favInfo != null">
        fav_info,
      </if>
      <if test="compositeNew != null">
        composite_new,
      </if>
      <if test="thirtySalesQuantity != null">
        thirty_sales_quantity,
      </if>
      <if test="promotionTitle != null">
        promotion_title,
      </if>
      <if test="promotionStartDate != null">
        promotion_start_date,
      </if>
      <if test="promotionEndDate != null">
        promotion_end_date,
      </if>
      <if test="dealSuggest != null">
        deal_suggest,
      </if>
      <if test="storeAttr != null">
        store_attr,
      </if>
      <if test="threeDaysSales != null">
        three_days_sales,
      </if>
      <if test="deliverycycleCode != null">
        deliverycycle_code,
      </if>
      <if test="newProduct != null">
        new_product,
      </if>
      <if test="goodsCutType != null">
        goods_cut_type,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
      <if test="reason != null">
        reason,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="applyNo != null">
        #{applyNo,jdbcType=VARCHAR},
      </if>
      <if test="applyLine != null">
        #{applyLine,jdbcType=VARCHAR},
      </if>
      <if test="companyCode != null">
        #{companyCode,jdbcType=VARCHAR},
      </if>
      <if test="storeCode != null">
        #{storeCode,jdbcType=VARCHAR},
      </if>
      <if test="applyDate != null">
        #{applyDate,jdbcType=DATE},
      </if>
      <if test="dataOriginType != null">
        #{dataOriginType,jdbcType=TINYINT},
      </if>
      <if test="goodsNo != null">
        #{goodsNo,jdbcType=VARCHAR},
      </if>
      <if test="applyGoodsType != null">
        #{applyGoodsType,jdbcType=TINYINT},
      </if>
      <if test="applyTotal != null">
        #{applyTotal,jdbcType=DECIMAL},
      </if>
      <if test="unqualifiedAwaitStock != null">
        #{unqualifiedAwaitStock,jdbcType=DECIMAL},
      </if>
      <if test="transitStock != null">
        #{transitStock,jdbcType=DECIMAL},
      </if>
      <if test="stock != null">
        #{stock,jdbcType=DECIMAL},
      </if>
      <if test="lockStock != null">
        #{lockStock,jdbcType=DECIMAL},
      </if>
      <if test="unqualifiedStock != null">
        #{unqualifiedStock,jdbcType=DECIMAL},
      </if>
      <if test="applyTransitStock != null">
        #{applyTransitStock,jdbcType=DECIMAL},
      </if>
      <if test="inTransitStock != null">
        #{inTransitStock,jdbcType=DECIMAL},
      </if>
      <if test="distrTransitStock != null">
        #{distrTransitStock,jdbcType=DECIMAL},
      </if>
      <if test="specialCtrl != null">
        #{specialCtrl,jdbcType=VARCHAR},
      </if>
      <if test="specialThirtyDaysQty != null">
        #{specialThirtyDaysQty,jdbcType=DECIMAL},
      </if>
      <if test="goodsLevel != null">
        #{goodsLevel,jdbcType=VARCHAR},
      </if>
      <if test="stockUpperLimitDays != null">
        #{stockUpperLimitDays,jdbcType=INTEGER},
      </if>
      <if test="stockLowerLimitDays != null">
        #{stockLowerLimitDays,jdbcType=INTEGER},
      </if>
      <if test="bdpAverageDailySales != null">
        #{bdpAverageDailySales,jdbcType=DECIMAL},
      </if>
      <if test="minDisplayQty != null">
        #{minDisplayQty,jdbcType=DECIMAL},
      </if>
      <if test="stockUpperLimit != null">
        #{stockUpperLimit,jdbcType=DECIMAL},
      </if>
      <if test="stockLowerLimit != null">
        #{stockLowerLimit,jdbcType=DECIMAL},
      </if>
      <if test="buyStock != null">
        #{buyStock,jdbcType=DECIMAL},
      </if>
      <if test="saleDaysBefore != null">
        #{saleDaysBefore,jdbcType=DECIMAL},
      </if>
      <if test="saleDaysAfter != null">
        #{saleDaysAfter,jdbcType=DECIMAL},
      </if>
      <if test="thirtyDaysSales != null">
        #{thirtyDaysSales,jdbcType=DECIMAL},
      </if>
      <if test="ninetyDaysSales != null">
        #{ninetyDaysSales,jdbcType=DECIMAL},
      </if>
      <if test="companyApplyTotal != null">
        #{companyApplyTotal,jdbcType=BIGINT},
      </if>
      <if test="storeApplyTotal != null">
        #{storeApplyTotal,jdbcType=BIGINT},
      </if>
      <if test="middlePackageSwitch != null">
        #{middlePackageSwitch,jdbcType=VARCHAR},
      </if>
      <if test="middlePackageQty != null">
        #{middlePackageQty,jdbcType=DECIMAL},
      </if>
      <if test="middleCodeFlag != null">
        #{middleCodeFlag,jdbcType=TINYINT},
      </if>
      <if test="applyRatio != null">
        #{applyRatio,jdbcType=DECIMAL},
      </if>
      <if test="categoryId != null">
        #{categoryId,jdbcType=BIGINT},
      </if>
      <if test="purchaseType != null">
        #{purchaseType,jdbcType=INTEGER},
      </if>
      <if test="purchaseChannel != null">
        #{purchaseChannel,jdbcType=VARCHAR},
      </if>
      <if test="warehouseCode != null">
        #{warehouseCode,jdbcType=VARCHAR},
      </if>
      <if test="recommendReason != null">
        #{recommendReason,jdbcType=VARCHAR},
      </if>
      <if test="promotionName != null">
        #{promotionName,jdbcType=VARCHAR},
      </if>
      <if test="promotionWay != null">
        #{promotionWay,jdbcType=VARCHAR},
      </if>
      <if test="thresholdInfo != null">
        #{thresholdInfo,jdbcType=VARCHAR},
      </if>
      <if test="favInfo != null">
        #{favInfo,jdbcType=VARCHAR},
      </if>
      <if test="compositeNew != null">
        #{compositeNew,jdbcType=TINYINT},
      </if>
      <if test="thirtySalesQuantity != null">
        #{thirtySalesQuantity,jdbcType=DECIMAL},
      </if>
      <if test="promotionTitle != null">
        #{promotionTitle,jdbcType=VARCHAR},
      </if>
      <if test="promotionStartDate != null">
        #{promotionStartDate,jdbcType=VARCHAR},
      </if>
      <if test="promotionEndDate != null">
        #{promotionEndDate,jdbcType=VARCHAR},
      </if>
      <if test="dealSuggest != null">
        #{dealSuggest,jdbcType=TINYINT},
      </if>
      <if test="storeAttr != null">
        #{storeAttr,jdbcType=TINYINT},
      </if>
      <if test="threeDaysSales != null">
        #{threeDaysSales,jdbcType=DECIMAL},
      </if>
      <if test="deliverycycleCode != null">
        #{deliverycycleCode,jdbcType=VARCHAR},
      </if>
      <if test="newProduct != null">
        #{newProduct,jdbcType=TINYINT},
      </if>
      <if test="goodsCutType != null">
        #{goodsCutType,jdbcType=INTEGER},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="reason != null">
        #{reason,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.iscm.entityTidb.IscmBdpApplyInfoExample" resultType="java.lang.Long">
    select count(*) from iscm_bdp_apply_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update iscm_bdp_apply_info
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.applyNo != null">
        apply_no = #{record.applyNo,jdbcType=VARCHAR},
      </if>
      <if test="record.applyLine != null">
        apply_line = #{record.applyLine,jdbcType=VARCHAR},
      </if>
      <if test="record.companyCode != null">
        company_code = #{record.companyCode,jdbcType=VARCHAR},
      </if>
      <if test="record.storeCode != null">
        store_code = #{record.storeCode,jdbcType=VARCHAR},
      </if>
      <if test="record.applyDate != null">
        apply_date = #{record.applyDate,jdbcType=DATE},
      </if>
      <if test="record.dataOriginType != null">
        data_origin_type = #{record.dataOriginType,jdbcType=TINYINT},
      </if>
      <if test="record.goodsNo != null">
        goods_no = #{record.goodsNo,jdbcType=VARCHAR},
      </if>
      <if test="record.applyGoodsType != null">
        apply_goods_type = #{record.applyGoodsType,jdbcType=TINYINT},
      </if>
      <if test="record.applyTotal != null">
        apply_total = #{record.applyTotal,jdbcType=DECIMAL},
      </if>
      <if test="record.unqualifiedAwaitStock != null">
        unqualified_await_stock = #{record.unqualifiedAwaitStock,jdbcType=DECIMAL},
      </if>
      <if test="record.transitStock != null">
        transit_stock = #{record.transitStock,jdbcType=DECIMAL},
      </if>
      <if test="record.stock != null">
        stock = #{record.stock,jdbcType=DECIMAL},
      </if>
      <if test="record.lockStock != null">
        lock_stock = #{record.lockStock,jdbcType=DECIMAL},
      </if>
      <if test="record.unqualifiedStock != null">
        unqualified_stock = #{record.unqualifiedStock,jdbcType=DECIMAL},
      </if>
      <if test="record.applyTransitStock != null">
        apply_transit_stock = #{record.applyTransitStock,jdbcType=DECIMAL},
      </if>
      <if test="record.inTransitStock != null">
        in_transit_stock = #{record.inTransitStock,jdbcType=DECIMAL},
      </if>
      <if test="record.distrTransitStock != null">
        distr_transit_stock = #{record.distrTransitStock,jdbcType=DECIMAL},
      </if>
      <if test="record.specialCtrl != null">
        special_ctrl = #{record.specialCtrl,jdbcType=VARCHAR},
      </if>
      <if test="record.specialThirtyDaysQty != null">
        special_thirty_days_qty = #{record.specialThirtyDaysQty,jdbcType=DECIMAL},
      </if>
      <if test="record.goodsLevel != null">
        goods_level = #{record.goodsLevel,jdbcType=VARCHAR},
      </if>
      <if test="record.stockUpperLimitDays != null">
        stock_upper_limit_days = #{record.stockUpperLimitDays,jdbcType=INTEGER},
      </if>
      <if test="record.stockLowerLimitDays != null">
        stock_lower_limit_days = #{record.stockLowerLimitDays,jdbcType=INTEGER},
      </if>
      <if test="record.bdpAverageDailySales != null">
        bdp_average_daily_sales = #{record.bdpAverageDailySales,jdbcType=DECIMAL},
      </if>
      <if test="record.minDisplayQty != null">
        min_display_qty = #{record.minDisplayQty,jdbcType=DECIMAL},
      </if>
      <if test="record.stockUpperLimit != null">
        stock_upper_limit = #{record.stockUpperLimit,jdbcType=DECIMAL},
      </if>
      <if test="record.stockLowerLimit != null">
        stock_lower_limit = #{record.stockLowerLimit,jdbcType=DECIMAL},
      </if>
      <if test="record.buyStock != null">
        buy_stock = #{record.buyStock,jdbcType=DECIMAL},
      </if>
      <if test="record.saleDaysBefore != null">
        sale_days_before = #{record.saleDaysBefore,jdbcType=DECIMAL},
      </if>
      <if test="record.saleDaysAfter != null">
        sale_days_after = #{record.saleDaysAfter,jdbcType=DECIMAL},
      </if>
      <if test="record.thirtyDaysSales != null">
        thirty_days_sales = #{record.thirtyDaysSales,jdbcType=DECIMAL},
      </if>
      <if test="record.ninetyDaysSales != null">
        ninety_days_sales = #{record.ninetyDaysSales,jdbcType=DECIMAL},
      </if>
      <if test="record.companyApplyTotal != null">
        company_apply_total = #{record.companyApplyTotal,jdbcType=BIGINT},
      </if>
      <if test="record.storeApplyTotal != null">
        store_apply_total = #{record.storeApplyTotal,jdbcType=BIGINT},
      </if>
      <if test="record.middlePackageSwitch != null">
        middle_package_switch = #{record.middlePackageSwitch,jdbcType=VARCHAR},
      </if>
      <if test="record.middlePackageQty != null">
        middle_package_qty = #{record.middlePackageQty,jdbcType=DECIMAL},
      </if>
      <if test="record.middleCodeFlag != null">
        middle_code_flag = #{record.middleCodeFlag,jdbcType=TINYINT},
      </if>
      <if test="record.applyRatio != null">
        apply_ratio = #{record.applyRatio,jdbcType=DECIMAL},
      </if>
      <if test="record.categoryId != null">
        category_id = #{record.categoryId,jdbcType=BIGINT},
      </if>
      <if test="record.purchaseType != null">
        purchase_type = #{record.purchaseType,jdbcType=INTEGER},
      </if>
      <if test="record.purchaseChannel != null">
        purchase_channel = #{record.purchaseChannel,jdbcType=VARCHAR},
      </if>
      <if test="record.warehouseCode != null">
        warehouse_code = #{record.warehouseCode,jdbcType=VARCHAR},
      </if>
      <if test="record.recommendReason != null">
        recommend_reason = #{record.recommendReason,jdbcType=VARCHAR},
      </if>
      <if test="record.promotionName != null">
        promotion_name = #{record.promotionName,jdbcType=VARCHAR},
      </if>
      <if test="record.promotionWay != null">
        promotion_way = #{record.promotionWay,jdbcType=VARCHAR},
      </if>
      <if test="record.thresholdInfo != null">
        threshold_info = #{record.thresholdInfo,jdbcType=VARCHAR},
      </if>
      <if test="record.favInfo != null">
        fav_info = #{record.favInfo,jdbcType=VARCHAR},
      </if>
      <if test="record.compositeNew != null">
        composite_new = #{record.compositeNew,jdbcType=TINYINT},
      </if>
      <if test="record.thirtySalesQuantity != null">
        thirty_sales_quantity = #{record.thirtySalesQuantity,jdbcType=DECIMAL},
      </if>
      <if test="record.promotionTitle != null">
        promotion_title = #{record.promotionTitle,jdbcType=VARCHAR},
      </if>
      <if test="record.promotionStartDate != null">
        promotion_start_date = #{record.promotionStartDate,jdbcType=VARCHAR},
      </if>
      <if test="record.promotionEndDate != null">
        promotion_end_date = #{record.promotionEndDate,jdbcType=VARCHAR},
      </if>
      <if test="record.dealSuggest != null">
        deal_suggest = #{record.dealSuggest,jdbcType=TINYINT},
      </if>
      <if test="record.storeAttr != null">
        store_attr = #{record.storeAttr,jdbcType=TINYINT},
      </if>
      <if test="record.threeDaysSales != null">
        three_days_sales = #{record.threeDaysSales,jdbcType=DECIMAL},
      </if>
      <if test="record.deliverycycleCode != null">
        deliverycycle_code = #{record.deliverycycleCode,jdbcType=VARCHAR},
      </if>
      <if test="record.newProduct != null">
        new_product = #{record.newProduct,jdbcType=TINYINT},
      </if>
      <if test="record.goodsCutType != null">
        goods_cut_type = #{record.goodsCutType,jdbcType=INTEGER},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtUpdate != null">
        gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.reason != null">
        reason = #{record.reason,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update iscm_bdp_apply_info
    set id = #{record.id,jdbcType=BIGINT},
      apply_no = #{record.applyNo,jdbcType=VARCHAR},
      apply_line = #{record.applyLine,jdbcType=VARCHAR},
      company_code = #{record.companyCode,jdbcType=VARCHAR},
      store_code = #{record.storeCode,jdbcType=VARCHAR},
      apply_date = #{record.applyDate,jdbcType=DATE},
      data_origin_type = #{record.dataOriginType,jdbcType=TINYINT},
      goods_no = #{record.goodsNo,jdbcType=VARCHAR},
      apply_goods_type = #{record.applyGoodsType,jdbcType=TINYINT},
      apply_total = #{record.applyTotal,jdbcType=DECIMAL},
      unqualified_await_stock = #{record.unqualifiedAwaitStock,jdbcType=DECIMAL},
      transit_stock = #{record.transitStock,jdbcType=DECIMAL},
      stock = #{record.stock,jdbcType=DECIMAL},
      lock_stock = #{record.lockStock,jdbcType=DECIMAL},
      unqualified_stock = #{record.unqualifiedStock,jdbcType=DECIMAL},
      apply_transit_stock = #{record.applyTransitStock,jdbcType=DECIMAL},
      in_transit_stock = #{record.inTransitStock,jdbcType=DECIMAL},
      distr_transit_stock = #{record.distrTransitStock,jdbcType=DECIMAL},
      special_ctrl = #{record.specialCtrl,jdbcType=VARCHAR},
      special_thirty_days_qty = #{record.specialThirtyDaysQty,jdbcType=DECIMAL},
      goods_level = #{record.goodsLevel,jdbcType=VARCHAR},
      stock_upper_limit_days = #{record.stockUpperLimitDays,jdbcType=INTEGER},
      stock_lower_limit_days = #{record.stockLowerLimitDays,jdbcType=INTEGER},
      bdp_average_daily_sales = #{record.bdpAverageDailySales,jdbcType=DECIMAL},
      min_display_qty = #{record.minDisplayQty,jdbcType=DECIMAL},
      stock_upper_limit = #{record.stockUpperLimit,jdbcType=DECIMAL},
      stock_lower_limit = #{record.stockLowerLimit,jdbcType=DECIMAL},
      buy_stock = #{record.buyStock,jdbcType=DECIMAL},
      sale_days_before = #{record.saleDaysBefore,jdbcType=DECIMAL},
      sale_days_after = #{record.saleDaysAfter,jdbcType=DECIMAL},
      thirty_days_sales = #{record.thirtyDaysSales,jdbcType=DECIMAL},
      ninety_days_sales = #{record.ninetyDaysSales,jdbcType=DECIMAL},
      company_apply_total = #{record.companyApplyTotal,jdbcType=BIGINT},
      store_apply_total = #{record.storeApplyTotal,jdbcType=BIGINT},
      middle_package_switch = #{record.middlePackageSwitch,jdbcType=VARCHAR},
      middle_package_qty = #{record.middlePackageQty,jdbcType=DECIMAL},
      middle_code_flag = #{record.middleCodeFlag,jdbcType=TINYINT},
      apply_ratio = #{record.applyRatio,jdbcType=DECIMAL},
      category_id = #{record.categoryId,jdbcType=BIGINT},
      purchase_type = #{record.purchaseType,jdbcType=INTEGER},
      purchase_channel = #{record.purchaseChannel,jdbcType=VARCHAR},
      warehouse_code = #{record.warehouseCode,jdbcType=VARCHAR},
      recommend_reason = #{record.recommendReason,jdbcType=VARCHAR},
      promotion_name = #{record.promotionName,jdbcType=VARCHAR},
      promotion_way = #{record.promotionWay,jdbcType=VARCHAR},
      threshold_info = #{record.thresholdInfo,jdbcType=VARCHAR},
      fav_info = #{record.favInfo,jdbcType=VARCHAR},
      composite_new = #{record.compositeNew,jdbcType=TINYINT},
      thirty_sales_quantity = #{record.thirtySalesQuantity,jdbcType=DECIMAL},
      promotion_title = #{record.promotionTitle,jdbcType=VARCHAR},
      promotion_start_date = #{record.promotionStartDate,jdbcType=VARCHAR},
      promotion_end_date = #{record.promotionEndDate,jdbcType=VARCHAR},
      deal_suggest = #{record.dealSuggest,jdbcType=TINYINT},
      store_attr = #{record.storeAttr,jdbcType=TINYINT},
      three_days_sales = #{record.threeDaysSales,jdbcType=DECIMAL},
      deliverycycle_code = #{record.deliverycycleCode,jdbcType=VARCHAR},
      new_product = #{record.newProduct,jdbcType=TINYINT},
      goods_cut_type = #{record.goodsCutType,jdbcType=INTEGER},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      reason = #{record.reason,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.iscm.entityTidb.IscmBdpApplyInfo">
    update iscm_bdp_apply_info
    <set>
      <if test="applyNo != null">
        apply_no = #{applyNo,jdbcType=VARCHAR},
      </if>
      <if test="applyLine != null">
        apply_line = #{applyLine,jdbcType=VARCHAR},
      </if>
      <if test="companyCode != null">
        company_code = #{companyCode,jdbcType=VARCHAR},
      </if>
      <if test="storeCode != null">
        store_code = #{storeCode,jdbcType=VARCHAR},
      </if>
      <if test="applyDate != null">
        apply_date = #{applyDate,jdbcType=DATE},
      </if>
      <if test="dataOriginType != null">
        data_origin_type = #{dataOriginType,jdbcType=TINYINT},
      </if>
      <if test="goodsNo != null">
        goods_no = #{goodsNo,jdbcType=VARCHAR},
      </if>
      <if test="applyGoodsType != null">
        apply_goods_type = #{applyGoodsType,jdbcType=TINYINT},
      </if>
      <if test="applyTotal != null">
        apply_total = #{applyTotal,jdbcType=DECIMAL},
      </if>
      <if test="unqualifiedAwaitStock != null">
        unqualified_await_stock = #{unqualifiedAwaitStock,jdbcType=DECIMAL},
      </if>
      <if test="transitStock != null">
        transit_stock = #{transitStock,jdbcType=DECIMAL},
      </if>
      <if test="stock != null">
        stock = #{stock,jdbcType=DECIMAL},
      </if>
      <if test="lockStock != null">
        lock_stock = #{lockStock,jdbcType=DECIMAL},
      </if>
      <if test="unqualifiedStock != null">
        unqualified_stock = #{unqualifiedStock,jdbcType=DECIMAL},
      </if>
      <if test="applyTransitStock != null">
        apply_transit_stock = #{applyTransitStock,jdbcType=DECIMAL},
      </if>
      <if test="inTransitStock != null">
        in_transit_stock = #{inTransitStock,jdbcType=DECIMAL},
      </if>
      <if test="distrTransitStock != null">
        distr_transit_stock = #{distrTransitStock,jdbcType=DECIMAL},
      </if>
      <if test="specialCtrl != null">
        special_ctrl = #{specialCtrl,jdbcType=VARCHAR},
      </if>
      <if test="specialThirtyDaysQty != null">
        special_thirty_days_qty = #{specialThirtyDaysQty,jdbcType=DECIMAL},
      </if>
      <if test="goodsLevel != null">
        goods_level = #{goodsLevel,jdbcType=VARCHAR},
      </if>
      <if test="stockUpperLimitDays != null">
        stock_upper_limit_days = #{stockUpperLimitDays,jdbcType=INTEGER},
      </if>
      <if test="stockLowerLimitDays != null">
        stock_lower_limit_days = #{stockLowerLimitDays,jdbcType=INTEGER},
      </if>
      <if test="bdpAverageDailySales != null">
        bdp_average_daily_sales = #{bdpAverageDailySales,jdbcType=DECIMAL},
      </if>
      <if test="minDisplayQty != null">
        min_display_qty = #{minDisplayQty,jdbcType=DECIMAL},
      </if>
      <if test="stockUpperLimit != null">
        stock_upper_limit = #{stockUpperLimit,jdbcType=DECIMAL},
      </if>
      <if test="stockLowerLimit != null">
        stock_lower_limit = #{stockLowerLimit,jdbcType=DECIMAL},
      </if>
      <if test="buyStock != null">
        buy_stock = #{buyStock,jdbcType=DECIMAL},
      </if>
      <if test="saleDaysBefore != null">
        sale_days_before = #{saleDaysBefore,jdbcType=DECIMAL},
      </if>
      <if test="saleDaysAfter != null">
        sale_days_after = #{saleDaysAfter,jdbcType=DECIMAL},
      </if>
      <if test="thirtyDaysSales != null">
        thirty_days_sales = #{thirtyDaysSales,jdbcType=DECIMAL},
      </if>
      <if test="ninetyDaysSales != null">
        ninety_days_sales = #{ninetyDaysSales,jdbcType=DECIMAL},
      </if>
      <if test="companyApplyTotal != null">
        company_apply_total = #{companyApplyTotal,jdbcType=BIGINT},
      </if>
      <if test="storeApplyTotal != null">
        store_apply_total = #{storeApplyTotal,jdbcType=BIGINT},
      </if>
      <if test="middlePackageSwitch != null">
        middle_package_switch = #{middlePackageSwitch,jdbcType=VARCHAR},
      </if>
      <if test="middlePackageQty != null">
        middle_package_qty = #{middlePackageQty,jdbcType=DECIMAL},
      </if>
      <if test="middleCodeFlag != null">
        middle_code_flag = #{middleCodeFlag,jdbcType=TINYINT},
      </if>
      <if test="applyRatio != null">
        apply_ratio = #{applyRatio,jdbcType=DECIMAL},
      </if>
      <if test="categoryId != null">
        category_id = #{categoryId,jdbcType=BIGINT},
      </if>
      <if test="purchaseType != null">
        purchase_type = #{purchaseType,jdbcType=INTEGER},
      </if>
      <if test="purchaseChannel != null">
        purchase_channel = #{purchaseChannel,jdbcType=VARCHAR},
      </if>
      <if test="warehouseCode != null">
        warehouse_code = #{warehouseCode,jdbcType=VARCHAR},
      </if>
      <if test="recommendReason != null">
        recommend_reason = #{recommendReason,jdbcType=VARCHAR},
      </if>
      <if test="promotionName != null">
        promotion_name = #{promotionName,jdbcType=VARCHAR},
      </if>
      <if test="promotionWay != null">
        promotion_way = #{promotionWay,jdbcType=VARCHAR},
      </if>
      <if test="thresholdInfo != null">
        threshold_info = #{thresholdInfo,jdbcType=VARCHAR},
      </if>
      <if test="favInfo != null">
        fav_info = #{favInfo,jdbcType=VARCHAR},
      </if>
      <if test="compositeNew != null">
        composite_new = #{compositeNew,jdbcType=TINYINT},
      </if>
      <if test="thirtySalesQuantity != null">
        thirty_sales_quantity = #{thirtySalesQuantity,jdbcType=DECIMAL},
      </if>
      <if test="promotionTitle != null">
        promotion_title = #{promotionTitle,jdbcType=VARCHAR},
      </if>
      <if test="promotionStartDate != null">
        promotion_start_date = #{promotionStartDate,jdbcType=VARCHAR},
      </if>
      <if test="promotionEndDate != null">
        promotion_end_date = #{promotionEndDate,jdbcType=VARCHAR},
      </if>
      <if test="dealSuggest != null">
        deal_suggest = #{dealSuggest,jdbcType=TINYINT},
      </if>
      <if test="storeAttr != null">
        store_attr = #{storeAttr,jdbcType=TINYINT},
      </if>
      <if test="threeDaysSales != null">
        three_days_sales = #{threeDaysSales,jdbcType=DECIMAL},
      </if>
      <if test="deliverycycleCode != null">
        deliverycycle_code = #{deliverycycleCode,jdbcType=VARCHAR},
      </if>
      <if test="newProduct != null">
        new_product = #{newProduct,jdbcType=TINYINT},
      </if>
      <if test="goodsCutType != null">
        goods_cut_type = #{goodsCutType,jdbcType=INTEGER},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="reason != null">
        reason = #{reason,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.iscm.entityTidb.IscmBdpApplyInfo">
    update iscm_bdp_apply_info
    set apply_no = #{applyNo,jdbcType=VARCHAR},
      apply_line = #{applyLine,jdbcType=VARCHAR},
      company_code = #{companyCode,jdbcType=VARCHAR},
      store_code = #{storeCode,jdbcType=VARCHAR},
      apply_date = #{applyDate,jdbcType=DATE},
      data_origin_type = #{dataOriginType,jdbcType=TINYINT},
      goods_no = #{goodsNo,jdbcType=VARCHAR},
      apply_goods_type = #{applyGoodsType,jdbcType=TINYINT},
      apply_total = #{applyTotal,jdbcType=DECIMAL},
      unqualified_await_stock = #{unqualifiedAwaitStock,jdbcType=DECIMAL},
      transit_stock = #{transitStock,jdbcType=DECIMAL},
      stock = #{stock,jdbcType=DECIMAL},
      lock_stock = #{lockStock,jdbcType=DECIMAL},
      unqualified_stock = #{unqualifiedStock,jdbcType=DECIMAL},
      apply_transit_stock = #{applyTransitStock,jdbcType=DECIMAL},
      in_transit_stock = #{inTransitStock,jdbcType=DECIMAL},
      distr_transit_stock = #{distrTransitStock,jdbcType=DECIMAL},
      special_ctrl = #{specialCtrl,jdbcType=VARCHAR},
      special_thirty_days_qty = #{specialThirtyDaysQty,jdbcType=DECIMAL},
      goods_level = #{goodsLevel,jdbcType=VARCHAR},
      stock_upper_limit_days = #{stockUpperLimitDays,jdbcType=INTEGER},
      stock_lower_limit_days = #{stockLowerLimitDays,jdbcType=INTEGER},
      bdp_average_daily_sales = #{bdpAverageDailySales,jdbcType=DECIMAL},
      min_display_qty = #{minDisplayQty,jdbcType=DECIMAL},
      stock_upper_limit = #{stockUpperLimit,jdbcType=DECIMAL},
      stock_lower_limit = #{stockLowerLimit,jdbcType=DECIMAL},
      buy_stock = #{buyStock,jdbcType=DECIMAL},
      sale_days_before = #{saleDaysBefore,jdbcType=DECIMAL},
      sale_days_after = #{saleDaysAfter,jdbcType=DECIMAL},
      thirty_days_sales = #{thirtyDaysSales,jdbcType=DECIMAL},
      ninety_days_sales = #{ninetyDaysSales,jdbcType=DECIMAL},
      company_apply_total = #{companyApplyTotal,jdbcType=BIGINT},
      store_apply_total = #{storeApplyTotal,jdbcType=BIGINT},
      middle_package_switch = #{middlePackageSwitch,jdbcType=VARCHAR},
      middle_package_qty = #{middlePackageQty,jdbcType=DECIMAL},
      middle_code_flag = #{middleCodeFlag,jdbcType=TINYINT},
      apply_ratio = #{applyRatio,jdbcType=DECIMAL},
      category_id = #{categoryId,jdbcType=BIGINT},
      purchase_type = #{purchaseType,jdbcType=INTEGER},
      purchase_channel = #{purchaseChannel,jdbcType=VARCHAR},
      warehouse_code = #{warehouseCode,jdbcType=VARCHAR},
      recommend_reason = #{recommendReason,jdbcType=VARCHAR},
      promotion_name = #{promotionName,jdbcType=VARCHAR},
      promotion_way = #{promotionWay,jdbcType=VARCHAR},
      threshold_info = #{thresholdInfo,jdbcType=VARCHAR},
      fav_info = #{favInfo,jdbcType=VARCHAR},
      composite_new = #{compositeNew,jdbcType=TINYINT},
      thirty_sales_quantity = #{thirtySalesQuantity,jdbcType=DECIMAL},
      promotion_title = #{promotionTitle,jdbcType=VARCHAR},
      promotion_start_date = #{promotionStartDate,jdbcType=VARCHAR},
      promotion_end_date = #{promotionEndDate,jdbcType=VARCHAR},
      deal_suggest = #{dealSuggest,jdbcType=TINYINT},
      store_attr = #{storeAttr,jdbcType=TINYINT},
      three_days_sales = #{threeDaysSales,jdbcType=DECIMAL},
      deliverycycle_code = #{deliverycycleCode,jdbcType=VARCHAR},
      new_product = #{newProduct,jdbcType=TINYINT},
      goods_cut_type = #{goodsCutType,jdbcType=INTEGER},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      reason = #{reason,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>