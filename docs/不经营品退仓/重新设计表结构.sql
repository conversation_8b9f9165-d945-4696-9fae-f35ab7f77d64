-- ========================================
-- 不经营品退仓表结构重新设计（基于整体闭环）
-- 参考 importReturnWarehouseExec 逻辑
-- 数据最终落地到 iscm_store_return_execute_order_main 和 iscm_store_return_execute_order_detail
-- ========================================

-- 1. 枚举扩展
-- ReturnWarehouseBusinessTypeEnum 新增：
-- UNMANAGE_GOODS_RETURN((byte)8, "不经营品退仓")
-- 
-- ReturnTypeEnum 复用现有：
-- DEFECTIVE(3, "不良品退仓") // 不经营品属于不良品的一种

-- ========================================
-- 核心设计思路：
-- 1. 简化表结构，只保留必要的任务跟踪和进度管理
-- 2. 参考导入逻辑，直接生成退仓执行单
-- 3. 使用Redis缓存处理进度（参考导入逻辑）
-- 4. 异步处理，按门店分组
-- ========================================

-- 1. 主表：不经营品退仓任务表（简化版）
CREATE TABLE `unmanage_goods_return_task` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `task_code` varchar(64) NOT NULL COMMENT '任务编码',
  `task_name` varchar(128) NOT NULL COMMENT '任务名称',
  `platform_org_id` bigint(20) NOT NULL COMMENT '平台机构ID',
  `platform_name` varchar(128) NOT NULL COMMENT '平台名称',
  `org_type` tinyint(4) NOT NULL COMMENT '选中机构类型：1-参数配置 2-自选门店',
  `org_ids` text NOT NULL COMMENT '选中机构org_ids集合，逗号隔开',
  `goods_type` tinyint(4) NOT NULL COMMENT '商品类型：1-全部不经营商品 2-自定义商品',
  `goods_nos`  text COMMENT '选中商品goods_nos集合，自定义时必填，逗号隔开',
  `store_count` int(11) DEFAULT 0 COMMENT '涉及门店数量',
  `goods_count` int(11) DEFAULT 0 COMMENT '涉及商品数量',
  `total_return_qty` decimal(18,4) DEFAULT 0 COMMENT '总退仓数量',
  `total_return_amount` decimal(18,4) DEFAULT 0 COMMENT '总退仓金额',
  `task_status` tinyint(4) NOT NULL DEFAULT 1 COMMENT '任务状态：1-计算中 2-计算完成 3-处理失败',
  `process_count` int(11) DEFAULT 0 COMMENT '总处理数量',
  `success_count` int(11) DEFAULT 0 COMMENT '成功处理数量',
  `error_count` int(11) DEFAULT 0 COMMENT '错误处理数量',
  `error_msg`  text COMMENT '错误信息',
  `start_time` datetime COMMENT '开始时间',
  `end_time` datetime COMMENT '结束时间',
  `status` tinyint(4) NOT NULL DEFAULT 0 COMMENT '状态(-1删除，0正常)',
  `gmt_create` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `gmt_update` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `extend` text COMMENT '扩展字段',
  `version` int(11) NOT NULL DEFAULT 1 COMMENT '版本号',
  `created_by` bigint(20) NOT NULL COMMENT '创建人ID',
  `created_name` varchar(64) NOT NULL COMMENT '创建人',
  `updated_by` bigint(20) COMMENT '更新人ID',
  `updated_name` varchar(64) COMMENT '更新人',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_task_code` (`task_code`),
  KEY `idx_platform_org_id` (`platform_org_id`),
  KEY `idx_task_status` (`task_status`),
  KEY `idx_created_by` (`created_by`),
  KEY `idx_gmt_create` (`gmt_create`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='不经营品退仓任务表';
